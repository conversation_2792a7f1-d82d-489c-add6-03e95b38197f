import { HttpStatus, Injectable } from '@nestjs/common';
import {
  Messages,
  SUCCESS_UPDATED,
  SUCCESS_ADDED,
  SUCCESS_DELETED,
} from 'src/core/constantes';
import { DataInput } from 'src/core/defs';
import { BaseService } from 'src/core/service';
import { LUX_SYS_TABLES } from '../models/sys.schema';
import { LUX_COMPTA_TABLES } from '@app/compta/models/compta.schema';
import { LUX_GCOM_VENTE_TABLES } from '@app/gcom/vente/models/vente.schema';

@Injectable()
export class MediaService extends BaseService {
  getEcritureTableName(journal: string) {
    switch (journal) {
      case 'ac':
        return LUX_COMPTA_TABLES.table_compta_ecriture_achat;
      case 'bq':
        return LUX_COMPTA_TABLES.table_compta_ecriture_banque;
      case 'ca':
        return LUX_COMPTA_TABLES.table_compta_ecriture_caisse;
      case 'od':
        return LUX_COMPTA_TABLES.table_compta_ecriture_od;
      case 've':
        return LUX_COMPTA_TABLES.table_compta_ecriture_vente;
      
    }
    return '';
  }
  // kjskks
  async getMedia(dataUrl: any, params: any = {}) {
    try {
      let liste_media = [];
      liste_media = await this.getManager().find(
        LUX_SYS_TABLES.table_sys_media,
      );
      return this.sendData(
        {
          dl: [...liste_media],
        },
        params,
      );
    } catch (error) {
      return this.sendError(HttpStatus.BAD_REQUEST, error);
    }
  }

  async editMedia(id: number, input: DataInput, dataUrl: any = {}) {
    try {
      const dbObjet: any = await this.getManager().findOne(
        LUX_SYS_TABLES.table_sys_media,
        {
          where: { id },
        },
      );
      if (dbObjet) {
        await this.getManager().save(
          LUX_SYS_TABLES.table_sys_media,
          input.data,
        );
        return this.getMedia(dataUrl, {
          ...SUCCESS_UPDATED,
        });
      } else {
        return this.sendError(
          HttpStatus.BAD_REQUEST,
          Messages.RESSOURCE_INTROUVABLE,
        );
      }
    } catch (error) {
      console.log(error);
      return this.sendError(HttpStatus.BAD_REQUEST, error);
    }
  }

  async addEcritureWithMedia(journal: string, input: any, dataUrl: any) {
    try {
      const data = input.data;
      data.objet.heure_doc = this.getCurrentTime();
      if (data.objet.mode_paiement == 'prlv') {
        data.objet.date_validation = data.objet.date_doc;
        data.objet.data_etat = { etat: 'encaisse' };
      }
      const ecriture = await this.getManager().save(
        this.getEcritureTableName(journal),
        data.objet,
      );
      for (const file of input.files) {
        const media = {
          code: data.code,
          mdl: data.mdl,
          data_options: {
            id: ecriture.id,
            name: file.filename,
            type: file.mimetype,
          },
        };
        await this.getManager().save(LUX_SYS_TABLES.table_sys_media, media);
      }
      if (ecriture) {
        const files = await this.getRepository(LUX_SYS_TABLES.table_sys_media)
          .createQueryBuilder('media')
          .select(['media.*'])
          .where('media.mdl = :mdl', { mdl: data.mdl })
          .andWhere("CAST(media.data_options->>'id' AS integer) = :id", {
            id: ecriture.id,
          })
          .getRawMany();
        return this.sendData(
          {
            mdl: {
              liste_ecritures: await this.loadEcritures({
                ...dataUrl,
                journal,
              }),
            },
            dl: [...files],
            ecriture,
          },
          SUCCESS_UPDATED,
        );
      }
    } catch (error) {
      console.log(error);
      return this.sendError(HttpStatus.BAD_REQUEST, error);
    }
  }

  async addDepenseMedia(input: any) {
    try {
      const data = input.data;
      const journee: any = await this.getManager().findOne(
        LUX_GCOM_VENTE_TABLES.table_gcom_journee,
        {
          where: { id: data.objet.id },
        },
      );
      for (const file of input.files) {
        const media = {
          code: 'fichiers_dep',
          mdl: 'depenses',
          data_options: {
            id: journee.id,
            name: file.filename,
            type: file.mimetype,
          },
        };
        const MediaAdded: any = await this.getManager().save(
          LUX_SYS_TABLES.table_sys_media,
          media,
        );
        journee.data_depense[data.responsable][data.rubrique][data.index][
          'files'
        ].push(MediaAdded.id);
      }
      const updatedJournee = await this.getManager().save(
        LUX_GCOM_VENTE_TABLES.table_gcom_journee,
        journee,
      );
      if (updatedJournee) {
        const files = await this.getRepository(LUX_SYS_TABLES.table_sys_media)
          .createQueryBuilder('media')
          .select(['media.*'])
          .where('media.mdl = :mdl', { mdl: 'depenses' })
          .andWhere("CAST(media.data_options->>'id' AS integer) = :id", {
            id: journee.id,
          })
          .getRawMany();
        return this.sendData(
          {
            dl: [...files],
            journee: journee,
          },
          SUCCESS_ADDED,
        );
      }
    } catch (error) {
      console.log(error);
      return this.sendError(HttpStatus.BAD_REQUEST, error);
    }
  }

  async addMedia(input: any) {
    const data = input.data;
    try {
      for (const file of input.files) {
        const media = {
          code: data.code,
          mdl: data.mdl,
          data_options: {
            id: data.objet.id,
            name: file.filename,
            type: file.mimetype,
          },
        };
        await this.getManager().save(LUX_SYS_TABLES.table_sys_media, media);
      }
      const files = await this.getRepository(LUX_SYS_TABLES.table_sys_media)
        .createQueryBuilder('media')
        .select(['media.*'])
        .where('media.mdl = :mdl', { mdl: data.mdl })
        .andWhere("CAST(media.data_options->>'id' AS integer) = :id", {
          id: data.objet.id,
        })
        .getRawMany();
      return this.sendData(
        {
          dl: [...files],
        },
        SUCCESS_ADDED,
      );
    } catch (error) {
      return this.sendError(HttpStatus.BAD_REQUEST, error);
    }
  }
  async getEcritures(journal: string, params: any = {}) {
    try {
      return this.sendData(
        {
          mdl: {
            liste_ecritures: await this.loadEcritures({
              ...params.dataUrl,
              journal,
            }),
          },
        },
        params,
      );
    } catch (error) {
      return this.sendError(HttpStatus.BAD_REQUEST, error);
    }
  }
  async loadEcritures(dataUrl: any = {}) {
    let sql = 'vi=true';
    const args = {};
    if (dataUrl && dataUrl.journal) {
      if (dataUrl.tiers) {
        if (sql != '') {
          sql += ' and ';
        }
        sql += 'tiers = :tiers';
        args['tiers'] = dataUrl.tiers;
      }
      if (
        dataUrl.tpe_tiers &&
        dataUrl.tpe_tiers != '' &&
        dataUrl.tpe_tiers != 'null' &&
        dataUrl.tpe_tiers != 'undefined'
      ) {
        if (sql != '') {
          sql += ' and ';
        }
        sql += "data_tiers->>'tpe_tiers' = :tpe_tiers";
        args['tpe_tiers'] = dataUrl.tpe_tiers;
      }
      if (dataUrl.mois && dataUrl.annee) {
        if (sql != '') {
          sql += ' and ';
        }
        if (dataUrl.isSuivis == 'true')
          sql +=
            'EXTRACT(MONTH FROM date_echeance) = :mois AND EXTRACT(YEAR FROM date_echeance) = :annee';
        else
          sql +=
            'EXTRACT(MONTH FROM date_doc) = :mois AND EXTRACT(YEAR FROM date_doc) = :annee';
        args['mois'] = parseInt(dataUrl.mois);
        args['annee'] = parseInt(dataUrl.annee);
      }
      if (dataUrl.caisse) {
        if (sql != '') {
          sql += ' and ';
        }
        sql += 'journee_caisse = :caisse';
        args['caisse'] = dataUrl.caisse;
      }
    }
    if (sql) {
      if (Array.isArray(dataUrl.journal)) {
        const result = {};
        for (const jrnl of <string[]>dataUrl.journal) {
          result[jrnl] = await this.getRepository(
            this.getEcritureTableName(jrnl),
          )
            .createQueryBuilder()
            .where(sql, { ...args })
            .getMany();
        }
        let liste = [];
        Object.keys(result).forEach((jr) => {
          liste = liste.concat(result[jr]);
        });
        return liste;
      } else {
        return this.getRepository(this.getEcritureTableName(dataUrl.journal))
          .createQueryBuilder()
          .where(sql, { ...args })
          .getMany();
      }
    }
    return [];
  }

  async deleteMedia(id: number, dataUrl: any = {}) {
    try {
      const dbObjet = await this.getManager().findOne(
        LUX_SYS_TABLES.table_sys_media,
        {
          where: { id },
        },
      );
      if (dbObjet) {
        await this.getManager().delete(LUX_SYS_TABLES.table_sys_media, id);
        const files = await this.getRepository(LUX_SYS_TABLES.table_sys_media)
          .createQueryBuilder('media')
          .select(['media.*'])
          .where('media.mdl = :mdl', { mdl: dataUrl.mdl })
          .andWhere("CAST(media.data_options->>'id' AS integer) = :id", {
            id: dataUrl.mdl_id,
          })
          .getRawMany();
        return this.sendData(
          {
            dl: [...files],
          },
          SUCCESS_DELETED,
        );
      } else {
        return this.sendError(
          HttpStatus.BAD_REQUEST,
          Messages.RESSOURCE_INTROUVABLE,
        );
      }
    } catch (error) {
      console.log(error);
      return this.sendError(HttpStatus.BAD_REQUEST, error);
    }
  }

  async deleteMediaPaiement(id: number, dataUrl: any = {}) {
    try {
      const pjPaiement = [];
      let ecritures = [];
      const dbObjet = await this.getManager().findOne(
        LUX_SYS_TABLES.table_sys_media,
        {
          where: { id },
        },
      );
      await this.getManager().delete(LUX_SYS_TABLES.table_sys_media, id);
      if (dbObjet) {
        if (dataUrl.tpe === 'regl_frs') {
          ecritures = await this.getRepository(
            this.getEcritureTableName('bq'),
          ).find({
            where: {
              tpe_doc: 'regl_frs',
            },
          });
        }
        if (dataUrl.tpe === 'reg_client') {
          ecritures = await this.getRepository(
            this.getEcritureTableName('bq'),
          ).find({
            where: {
              tpe_doc: 'reg_client',
            },
          });
        }
        if (dataUrl.tpe === 'regl_invest') {
          ecritures = await this.getRepository(
            this.getEcritureTableName('bq'),
          ).find({
            where: {
              tpe_doc: 'regl_invest',
            },
          });
        }
        const ecritureObjet: any = await this.getRepository(
          this.getEcritureTableName('bq'),
        ).findOne({
          where: {
            id: dataUrl.mdl_id,
          },
        });
        const filteredEcritures = ecritures.filter(
          (ecr) => ecr.data_paiement == ecritureObjet.data_paiement,
        );
        const filesPaiement = await this.getRepository(
          LUX_SYS_TABLES.table_sys_media,
        ).find({
          where: {
            mdl: 'bq',
          },
        });
        filteredEcritures.forEach((ecr) => {
          filesPaiement
            .filter((file) => file.data_options.id === ecr.id)
            .forEach((file) => {
              pjPaiement.push(file);
            });
        });
        return this.sendData(
          {
            dl: { pjPaiement },
          },
          SUCCESS_DELETED,
        );
      } else {
        return this.sendError(
          HttpStatus.BAD_REQUEST,
          Messages.RESSOURCE_INTROUVABLE,
        );
      }
    } catch (error) {
      console.log(error);
      return this.sendError(HttpStatus.BAD_REQUEST, error);
    }
  }

  async deleteDepenseMedia(id: number, dataUrl: any = {}) {
    try {
      const dbObjet = await this.getManager().findOne(
        LUX_SYS_TABLES.table_sys_media,
        {
          where: { id },
        },
      );
      if (dbObjet) {
        const journee: any = await this.getManager().findOne(
          LUX_GCOM_VENTE_TABLES.table_gcom_journee,
          {
            where: { id: dataUrl.journee },
          },
        );
        const indexToRemove =
          journee.data_depense[dataUrl.responsable][dataUrl.rubrique][
            dataUrl.index
          ]['files'].indexOf(id);
        journee.data_depense[dataUrl.responsable][dataUrl.rubrique][
          dataUrl.index
        ]['files'].splice(indexToRemove, 1);
        await this.getManager().save(
          LUX_GCOM_VENTE_TABLES.table_gcom_journee,
          journee,
        );
        await this.getManager().delete(LUX_SYS_TABLES.table_sys_media, id);
        const files = await this.getRepository(LUX_SYS_TABLES.table_sys_media)
          .createQueryBuilder('media')
          .select(['media.*'])
          .where('media.mdl = :mdl', { mdl: 'depenses' })
          .andWhere("CAST(media.data_options->>'id' AS integer) = :id", {
            id: dataUrl.journee,
          })
          .getRawMany();
        return this.sendData(
          {
            dl: [...files],
            journee: journee,
          },
          SUCCESS_DELETED,
        );
      } else {
        return this.sendError(
          HttpStatus.BAD_REQUEST,
          Messages.RESSOURCE_INTROUVABLE,
        );
      }
    } catch (error) {
      console.log(error);
      return this.sendError(HttpStatus.BAD_REQUEST, error);
    }
  }
}
